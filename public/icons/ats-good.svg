<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_9217_3687)">
<rect width="40" height="40" rx="10" fill="url(#paint0_linear_9217_3687)"/>
<g filter="url(#filter1_dd_9217_3687)">
<path d="M25.0002 11.3399C26.5085 12.2107 27.7632 13.4604 28.64 14.9652C29.5169 16.47 29.9856 18.1777 29.9997 19.9192C30.0137 21.6608 29.5727 23.3758 28.7203 24.8946C27.8678 26.4133 26.6334 27.6831 25.1394 28.5782C23.6454 29.4733 21.9435 29.9627 20.2022 29.998C18.4609 30.0332 16.7406 29.6131 15.2116 28.7791C13.6826 27.9452 12.3979 26.7264 11.4847 25.2434C10.5715 23.7604 10.0614 22.0646 10.005 20.3239L10 19.9999L10.005 19.6759C10.061 17.9489 10.5636 16.2659 11.4637 14.7909C12.3638 13.3159 13.6307 12.0993 15.141 11.2598C16.6512 10.4202 18.3532 9.98629 20.0811 10.0003C21.809 10.0144 23.5038 10.4759 25.0002 11.3399ZM23.7072 17.2929C23.535 17.1207 23.3059 17.0173 23.0628 17.002C22.8198 16.9867 22.5796 17.0606 22.3872 17.2099L22.2932 17.2929L19.0001 20.5849L17.7071 19.2929L17.6131 19.2099C17.4207 19.0607 17.1805 18.9869 16.9375 19.0022C16.6945 19.0176 16.4655 19.121 16.2933 19.2932C16.1212 19.4653 16.0178 19.6943 16.0024 19.9373C15.9871 20.1803 16.0609 20.4205 16.2101 20.6129L16.2931 20.7069L18.2931 22.7069L18.3871 22.7899C18.5625 22.926 18.7781 22.9998 19.0001 22.9998C19.2221 22.9998 19.4377 22.926 19.6131 22.7899L19.7071 22.7069L23.7072 18.7069L23.7902 18.6129C23.9394 18.4205 24.0133 18.1802 23.9981 17.9372C23.9828 17.6942 23.8793 17.4651 23.7072 17.2929Z" fill="url(#paint1_linear_9217_3687)" shape-rendering="crispEdges"/>
<path d="M20.0771 10.5C21.7185 10.5134 23.3285 10.9518 24.75 11.7725C26.1828 12.5998 27.375 13.7873 28.208 15.2168C29.041 16.6462 29.4866 18.2685 29.5 19.9229C29.5134 21.5772 29.0939 23.2067 28.2842 24.6494C27.4744 26.0921 26.302 27.2991 24.8828 28.1494C23.4636 28.9997 21.8465 29.4645 20.1924 29.498C18.5382 29.5315 16.9037 29.1321 15.4512 28.3398C13.9986 27.5476 12.7777 26.3903 11.9102 24.9814C11.0968 23.6606 10.6201 22.1619 10.5195 20.6172L10.5049 20.3076L10.5 20.0078L10.5049 19.6924C10.5581 18.0518 11.0356 16.4529 11.8906 15.0518C12.7457 13.6506 13.9492 12.4949 15.3838 11.6973C16.8185 10.8997 18.4357 10.4867 20.0771 10.5ZM23.0938 16.5029C22.7295 16.4801 22.3695 16.5909 22.0811 16.8145L22.0566 16.835L21.9619 16.918L21.9395 16.9395L18.999 19.8779L18.0605 18.9395L18.0381 18.918L17.9443 18.835C17.9363 18.8279 17.9274 18.821 17.9189 18.8145C17.6305 18.591 17.2704 18.48 16.9062 18.5029C16.5418 18.5259 16.1977 18.6812 15.9395 18.9395C15.6813 19.1977 15.5269 19.5418 15.5039 19.9062C15.481 20.2704 15.591 20.6305 15.8145 20.9189C15.8209 20.9272 15.828 20.9355 15.835 20.9434L15.918 21.0381C15.9248 21.0458 15.9321 21.0532 15.9395 21.0605L17.9395 23.0605C17.9468 23.0679 17.9542 23.0752 17.9619 23.082L18.0566 23.165C18.0644 23.1719 18.0728 23.1782 18.0811 23.1846C18.3441 23.3886 18.6671 23.5 19 23.5C19.333 23.5 19.6569 23.3887 19.9199 23.1846C19.9281 23.1782 19.9365 23.1719 19.9443 23.165L20.0381 23.082C20.0458 23.0752 20.0533 23.0678 20.0605 23.0605L24.0605 19.0605C24.0678 19.0533 24.0752 19.0458 24.082 19.0381L24.165 18.9434C24.172 18.9355 24.1791 18.9272 24.1855 18.9189C24.4091 18.6305 24.5199 18.2705 24.4971 17.9062C24.4741 17.5417 24.3188 17.1977 24.0605 16.9395C23.8023 16.6812 23.4583 16.5259 23.0938 16.5029Z" stroke="url(#paint2_linear_9217_3687)" stroke-linecap="round" stroke-linejoin="round" shape-rendering="crispEdges"/>
</g>
</g>
<defs>
<filter id="filter0_ii_9217_3687" x="0" y="-2" width="40" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_9217_3687"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect2_innerShadow_9217_3687"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_9217_3687" result="effect2_innerShadow_9217_3687"/>
</filter>
<filter id="filter1_dd_9217_3687" x="6" y="8" width="28" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9217_3687"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_9217_3687" result="effect2_dropShadow_9217_3687"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_9217_3687" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9217_3687" x1="20" y1="0" x2="20" y2="40" gradientUnits="userSpaceOnUse">
<stop stop-color="#68D1BF"/>
<stop offset="1" stop-color="#35746E"/>
</linearGradient>
<linearGradient id="paint1_linear_9217_3687" x1="20" y1="10" x2="20" y2="30" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint2_linear_9217_3687" x1="20" y1="10" x2="20" y2="30" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0.6"/>
</linearGradient>
</defs>
</svg>
