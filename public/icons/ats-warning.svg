<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_9323_311)">
<rect width="40" height="40" rx="12" fill="url(#paint0_linear_9323_311)"/>
<g filter="url(#filter1_dd_9323_311)">
<path d="M19.9992 9C20.5062 9.00033 21.0056 9.11677 21.4551 9.33944C21.9045 9.5621 22.2907 9.88441 22.5809 10.279L22.6947 10.443L31.5787 24.517C31.8449 24.955 31.9897 25.4499 31.9995 25.9555C32.0092 26.461 31.8837 26.9607 31.6346 27.4077C31.3856 27.8547 31.0211 28.2344 30.5753 28.5113C30.1294 28.7882 29.6169 28.9532 29.0856 28.991L28.8748 29H11.113C10.5811 28.994 10.0594 28.8602 9.59665 28.6112C9.13389 28.3622 8.7451 28.006 8.46652 27.5759C8.18795 27.1458 8.02867 26.6558 8.00352 26.1515C7.97838 25.6473 8.08819 25.1451 8.32273 24.692L8.42916 24.503L17.3069 10.438C17.5877 9.99901 17.9829 9.63609 18.4545 9.38421C18.9261 9.13234 19.4581 9.00002 19.9992 9ZM20.073 23L19.9391 23.007C19.683 23.0359 19.447 23.153 19.2757 23.336C19.1045 23.519 19.0099 23.7552 19.0099 24C19.0099 24.2448 19.1045 24.481 19.2757 24.664C19.447 24.847 19.683 24.9641 19.9391 24.993L20.0624 25L20.1962 24.993C20.4524 24.9641 20.6884 24.847 20.8597 24.664C21.0309 24.481 21.1255 24.2448 21.1255 24C21.1255 23.7552 21.0309 23.519 20.8597 23.336C20.6884 23.153 20.4524 23.0359 20.1962 23.007L20.073 23ZM20.0624 15C19.8043 15 19.5552 15.09 19.3624 15.2527C19.1695 15.4155 19.0463 15.6397 19.0161 15.883L19.0087 16V20L19.0161 20.117C19.0465 20.36 19.1699 20.5841 19.3627 20.7466C19.5556 20.9091 19.8045 20.9989 20.0624 20.9989C20.3203 20.9989 20.5693 20.9091 20.7621 20.7466C20.955 20.5841 21.0783 20.36 21.1088 20.117L21.1162 20V16L21.1088 15.883C21.0786 15.6397 20.9554 15.4155 20.7625 15.2527C20.5696 15.09 20.3205 15 20.0624 15Z" fill="url(#paint1_linear_9323_311)" shape-rendering="crispEdges"/>
<path d="M19.999 9.5C20.4299 9.5003 20.8537 9.59898 21.2334 9.78711C21.5658 9.95178 21.8549 10.1803 22.084 10.4551L22.1777 10.5752L22.2705 10.71H22.2715L31.1562 24.7842C31.3742 25.1458 31.492 25.5518 31.5 25.9648C31.508 26.3807 31.4047 26.7935 31.1982 27.1641C30.9916 27.5349 30.6871 27.8536 30.3115 28.0869C29.9373 28.3193 29.5052 28.4584 29.0557 28.4912L28.8535 28.5H11.1191C10.6671 28.4949 10.2246 28.381 9.83398 28.1709C9.44337 27.9607 9.11735 27.6613 8.88574 27.3037C8.65452 26.9465 8.52366 26.5417 8.50293 26.127C8.48252 25.7177 8.56999 25.3088 8.75879 24.9375L8.86523 24.748L8.86426 24.7471L17.7285 10.707C17.9619 10.3423 18.2925 10.0377 18.6904 9.8252C19.0387 9.63921 19.4274 9.52934 19.8271 9.50488L19.999 9.5ZM19.9131 22.5078C19.9031 22.5083 19.8928 22.5086 19.8828 22.5098C19.5124 22.5516 19.1661 22.7219 18.9111 22.9941C18.6555 23.2673 18.5098 23.625 18.5098 24C18.5098 24.375 18.6555 24.7327 18.9111 25.0059C19.1661 25.2781 19.5124 25.4484 19.8828 25.4902C19.892 25.4913 19.9019 25.4917 19.9111 25.4922L20.0342 25.499C20.0523 25.5 20.0708 25.5 20.0889 25.499L20.2227 25.4922C20.2324 25.4917 20.2422 25.4913 20.252 25.4902C20.6225 25.4484 20.9696 25.2783 21.2246 25.0059C21.4803 24.7327 21.626 24.375 21.626 24C21.626 23.625 21.4803 23.2673 21.2246 22.9941C20.9696 22.7217 20.6225 22.5516 20.252 22.5098L20.2246 22.5078L20.1016 22.501H20.0469L19.9131 22.5078ZM21.6152 15.9688L21.6074 15.8516C21.6068 15.8415 21.6057 15.8313 21.6045 15.8213C21.5583 15.4492 21.3708 15.1114 21.085 14.8701C20.7997 14.6295 20.4356 14.5 20.0625 14.5C19.6895 14.5 19.3253 14.6296 19.04 14.8701C18.7541 15.1114 18.5657 15.4491 18.5195 15.8213L18.5166 15.8516L18.5098 15.9688C18.5091 15.9792 18.5088 15.9896 18.5088 16V20C18.5088 20.0104 18.5091 20.0208 18.5098 20.0312L18.5166 20.1484C18.5172 20.1585 18.5183 20.1687 18.5195 20.1787C18.5661 20.5505 18.7543 20.888 19.04 21.1289C19.3253 21.3693 19.6896 21.499 20.0625 21.499C20.4353 21.499 20.7988 21.3692 21.084 21.1289C21.3698 20.888 21.5579 20.5506 21.6045 20.1787C21.6057 20.1686 21.6068 20.1586 21.6074 20.1484L21.6152 20.0312C21.6159 20.0208 21.6162 20.0104 21.6162 20V16C21.6162 15.9896 21.6159 15.9792 21.6152 15.9688Z" stroke="url(#paint2_linear_9323_311)" stroke-linecap="round" stroke-linejoin="round" shape-rendering="crispEdges"/>
</g>
</g>
<defs>
<filter id="filter0_ii_9323_311" x="0" y="-2" width="40" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_9323_311"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect2_innerShadow_9323_311"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_9323_311" result="effect2_innerShadow_9323_311"/>
</filter>
<filter id="filter1_dd_9323_311" x="4" y="7" width="32" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9323_311"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_9323_311" result="effect2_dropShadow_9323_311"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_9323_311" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9323_311" x1="20" y1="0" x2="20" y2="40" gradientUnits="userSpaceOnUse">
<stop stop-color="#F3BD7F"/>
<stop offset="1" stop-color="#D9622B"/>
</linearGradient>
<linearGradient id="paint1_linear_9323_311" x1="20" y1="9" x2="20" y2="29" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint2_linear_9323_311" x1="20" y1="9" x2="20" y2="29" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0.6"/>
</linearGradient>
</defs>
</svg>
